{"name": "nel-assets", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "@types/pg": "^8.15.5", "@vitest/browser": "^3.2.3", "drizzle-kit": "^0.30.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "playwright": "^1.53.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "vite-plugin-devtools-json": "^0.2.0", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0"}, "dependencies": {"drizzle-orm": "^0.40.0", "pg": "^8.16.3", "postgres": "^3.4.5"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}