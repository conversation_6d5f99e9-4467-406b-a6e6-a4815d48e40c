import { pgTable, pgSchema, varchar, integer } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const neldata = pgSchema("neldata");


export const locationHierarchyInNeldata = neldata.table("location_hierarchy", {
	assetItem: varchar("Asset Item", { length: 50 }),
	assetIdValidationCategory: varchar("Asset ID Validation Category", { length: 50 }),
	description: varchar("Description", { length: 5000 }),
	owner: varchar("Owner", { length: 50 }),
	subType: varchar("SubType", { length: 50 }),
	location: varchar("Location", { length: 5000 }),
	wbs: varchar("WBS", { length: 5000 }),
	wbsDescription: varchar("WBS Description", { length: 5000 }),
	controlLine: varchar("ControlLine", { length: 50 }),
	chainage: varchar("Chainage", { length: 50 }),
	column11: integer("Column11"),
	locationId: varchar("Location ID", { length: 50 }),
	locationDescription: varchar("Location Description", { length: 5000 }),
	chainage1: varchar("Chainage_1", { length: 50 }),
	controlLine1: varchar("ControlLine_1", { length: 50 }),
	plant: varchar("Plant", { length: 50 }),
	facility: varchar("Facility", { length: 50 }),
	area: varchar("Area", { length: 5000 }),
	location1: varchar("Location_1", { length: 5000 }),
	column20: varchar("Column20", { length: 50 }),
	column21: varchar("Column21", { length: 50 }),
	column22: varchar("Column22", { length: 50 }),
	column23: varchar("Column23", { length: 50 }),
	column24: varchar("Column24", { length: 50 }),
	column25: varchar("Column25", { length: 50 }),
	column26: varchar("Column26", { length: 50 }),
	column27: varchar("Column27", { length: 50 }),
	column28: varchar("Column28", { length: 50 }),
	column29: varchar("Column29", { length: 50 }),
	column30: varchar("Column30", { length: 50 }),
	column31: varchar("Column31", { length: 50 }),
	column32: varchar("Column32", { length: 50 }),
	column33: varchar("Column33", { length: 50 }),
	column34: varchar("Column34", { length: 50 }),
	column35: varchar("Column35", { length: 50 }),
	column36: varchar("Column36", { length: 50 }),
	column37: varchar("Column37", { length: 50 }),
	column38: varchar("Column38", { length: 50 }),
	column39: varchar("Column39", { length: 50 }),
	column40: varchar("Column40", { length: 50 }),
	column41: varchar("Column41", { length: 50 }),
	column42: varchar("Column42", { length: 50 }),
	column43: varchar("Column43", { length: 50 }),
	column44: varchar("Column44", { length: 50 }),
	column45: varchar("Column45", { length: 50 }),
	column46: varchar("Column46", { length: 50 }),
	column47: varchar("Column47", { length: 50 }),
	column48: varchar("Column48", { length: 50 }),
	column49: varchar("Column49", { length: 50 }),
	column50: varchar("Column50", { length: 50 }),
	column51: varchar("Column51", { length: 50 }),
	column52: varchar("Column52", { length: 50 }),
	column53: varchar("Column53", { length: 50 }),
	column54: varchar("Column54", { length: 50 }),
	column55: varchar("Column55", { length: 50 }),
	column56: varchar("Column56", { length: 50 }),
	column57: varchar("Column57", { length: 50 }),
	column58: varchar("Column58", { length: 50 }),
	column59: varchar("Column59", { length: 50 }),
	column60: varchar("Column60", { length: 50 }),
	column61: varchar("Column61", { length: 50 }),
	column62: varchar("Column62", { length: 50 }),
	column63: varchar("Column63", { length: 50 }),
	column64: varchar("Column64", { length: 50 }),
	column65: varchar("Column65", { length: 50 }),
	column66: varchar("Column66", { length: 50 }),
	column67: varchar("Column67", { length: 50 }),
	column68: varchar("Column68", { length: 50 }),
	column69: varchar("Column69", { length: 50 }),
	column70: varchar("Column70", { length: 50 }),
	column71: varchar("Column71", { length: 50 }),
	column72: varchar("Column72", { length: 50 }),
	column73: varchar("Column73", { length: 50 }),
	column74: varchar("Column74", { length: 50 }),
	column75: varchar("Column75", { length: 50 }),
	column76: varchar("Column76", { length: 50 }),
	column77: varchar("Column77", { length: 50 }),
	column78: varchar("Column78", { length: 50 }),
	column79: varchar("Column79", { length: 50 }),
	column80: varchar("Column80", { length: 50 }),
	column81: varchar("Column81", { length: 50 }),
	column82: varchar("Column82", { length: 50 }),
	column83: varchar("Column83", { length: 50 }),
	column84: varchar("Column84", { length: 50 }),
	column85: varchar("Column85", { length: 50 }),
	column86: varchar("Column86", { length: 50 }),
	column87: varchar("Column87", { length: 50 }),
	column88: varchar("Column88", { length: 50 }),
	column89: varchar("Column89", { length: 50 }),
	column90: varchar("Column90", { length: 50 }),
	column91: varchar("Column91", { length: 50 }),
	column92: varchar("Column92", { length: 50 }),
	column93: varchar("Column93", { length: 50 }),
	column94: varchar("Column94", { length: 50 }),
	column95: varchar("Column95", { length: 50 }),
	column96: varchar("Column96", { length: 50 }),
	column97: varchar("Column97", { length: 50 }),
	column98: varchar("Column98", { length: 50 }),
	column99: varchar("Column99", { length: 50 }),
	column100: varchar("Column100", { length: 50 }),
	column101: varchar("Column101", { length: 50 }),
	column102: varchar("Column102", { length: 50 }),
	column103: varchar("Column103", { length: 50 }),
	column104: varchar("Column104", { length: 50 }),
	column105: varchar("Column105", { length: 50 }),
	column106: varchar("Column106", { length: 50 }),
	column107: varchar("Column107", { length: 50 }),
	column108: varchar("Column108", { length: 50 }),
	column109: varchar("Column109", { length: 50 }),
	column110: varchar("Column110", { length: 50 }),
	column111: varchar("Column111", { length: 50 }),
	column112: varchar("Column112", { length: 50 }),
	column113: varchar("Column113", { length: 50 }),
	column114: varchar("Column114", { length: 50 }),
	column115: varchar("Column115", { length: 50 }),
	column116: varchar("Column116", { length: 50 }),
	column117: varchar("Column117", { length: 50 }),
	column118: varchar("Column118", { length: 50 }),
	column119: varchar("Column119", { length: 50 }),
	column120: varchar("Column120", { length: 50 }),
	column121: varchar("Column121", { length: 50 }),
	column122: varchar("Column122", { length: 50 }),
	column123: varchar("Column123", { length: 50 }),
	column124: varchar("Column124", { length: 50 }),
	column125: varchar("Column125", { length: 50 }),
	column126: varchar("Column126", { length: 50 }),
	column127: varchar("Column127", { length: 50 }),
});
